{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue", "mtime": 1752489465823}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB0aW1lTGluZSBmcm9tICJjb20vdGltZUxpbmUiOwppbXBvcnQgeyBIaXN0b3J5TGlzdCB9IGZyb20gIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIjsKaW1wb3J0IHsKICBsaXN0LAogIGxpc3RCeVBhZ2UsCiAgaW5zZXJ0T3JVcGRhdGVUb2RvSXRlbSwKICBkZWxCeUlkcwp9IGZyb20gIkAvYXBpL2FjdGl2aXRpL0RnVG9kb0l0ZW0iOwppbXBvcnQgeyBtYXBTdGF0ZSwgbWFwTXV0YXRpb25zLCBtYXBBY3Rpb25zIH0gZnJvbSAndnVleCcKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7IHRpbWVMaW5lIH0sCiAgbmFtZTogInByb2Nlc3NUb2RvIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIG1vZHVsZTogIiIsCiAgICAgICAgICB0b2RvVXNlck5hbWU6ICIiLAogICAgICAgICAgaGFuZGxlVXNlck5hbWU6ICIiLAogICAgICAgICAgYXBwbHlVc2VyTmFtZTogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIuW+he<PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["processTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "processTodo.vue", "sourceRoot": "src/views/activiti/dgTodoItem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane name=\"db\">\n          <span slot=\"label\"><i class=\"el-icon-s-order\"></i>待办</span>\n        </el-tab-pane>\n        <el-tab-pane name=\"yb\">\n          <span slot=\"label\"><i class=\"el-icon-s-claim\"></i>已办</span>\n        </el-tab-pane>\n      </el-tabs>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n        @handleEvent=\"handleFilterEvent\"\n        :btnHidden=\"false\"\n      ></el-filter>\n\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block;word-break : normal;\"\n          label=\"事项标题\"\n          min-width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? '被退回'\n                    : '待办理'\n                  : '已办理'\n              \"\n              class=\"item\"\n              :type=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? 'warning'\n                    : 'danger'\n                  : 'primary'\n              \"\n            >\n            </el-badge>\n            <el-button type=\"text\" size=\"small\" @click=\"goPage(scope.row)\">{{\n              scope.row.itemName\n            }}</el-button>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"100\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"跳转\"\n              class=\"el-icon-discover\"\n              @click=\"goPage(scope.row)\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.moduleKey !== 'probfbk'\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              @click=\"deleteTodo(scope.row)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <el-dialog\n      title=\"待办详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"itemName\">\n              <el-input v-model=\"formInfo.itemName\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"itemContent\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.itemContent\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"formInfo.module\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"通知时间：\" prop=\"todoTime\">\n              <el-date-picker\n                v-model=\"formInfo.todoTime\"\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport {\n  list,\n  listByPage,\n  insertOrUpdateTodoItem,\n  delByIds\n} from \"@/api/activiti/DgTodoItem\";\nimport { mapState, mapMutations, mapActions } from 'vuex'\n\nexport default {\n  components: { timeLine },\n  name: \"processTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {\n          module: \"\",\n          todoUserName: \"\",\n          handleUserName: \"\",\n          applyUserName: \"\"\n        },\n        fieldList: [\n          { label: \"待办来源\", type: \"input\", value: \"module\" },\n          { label: \"待办人\", type: \"input\", value: \"todoUserName\" },\n          { label: \"处理人\", type: \"input\", value: \"handleUserName\" },\n          { label: \"发起人\", type: \"input\", value: \"applyUserName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"module\", label: \"待办来源\", minWidth: \"120\" },\n          { prop: \"taskName\", label: \"任务名称\", minWidth: \"140\" },\n          { prop: \"isHandleCn\", label: \"是否已办\", minWidth: \"80\" },\n          { prop: \"todoUserName\", label: \"待办人名称\", minWidth: \"100\" },\n          { prop: \"handleUserName\", label: \"处理人名称\", minWidth: \"100\" },\n          { prop: \"applyUserName\", label: \"发起人名称\", minWidth: \"100\" },\n          { prop: \"todoTime\", label: \"通知时间\", minWidth: \"150\" },\n          { prop: \"handleTime\", label: \"处理时间\", minWidth: \"150\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      params: {\n        isHandle: 0\n      },\n      activeName: \"db\",\n      selectRows: [],\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {},\n      isDisabled: false,\n      timeData: [],\n      timeLineShow: false,\n      processData: {\n        processDefinitionKey: \"\",\n        businessKey: \"\",\n        businessType: \"\",\n        variables: {},\n        nextUser: \"\",\n        processType: \"complete\"\n      }\n    };\n  },\n  // watch: {\n  //   //触发计算函数时执行\n  //   qxjlObjIdChange(val) {\n  //     this.getData({ objId: val });\n  //   }\n  // },\n  //计算函数\n  computed: {\n    ...mapState('todoList', {\n      savedActiveName: 'activeName',\n      savedFilterInfo: 'filterInfo',\n      savedPagination: 'pagination',\n      savedScrollPosition: 'scrollPosition',\n      savedTableData: 'tableData',\n      savedTotal: 'total'\n    })\n  },\n  created() {\n    // 从 store 恢复状态\n    this.restorePageState()\n  },\n  mounted() {\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      this.restoreScrollPosition()\n      // 添加滚动监听\n      this.addScrollListener()\n    })\n  },\n\n  // keep-alive组件激活时调用\n  activated() {\n    // 每次激活时恢复滚动位置\n    this.$nextTick(() => {\n      // 使用setTimeout确保页面完全渲染\n      setTimeout(() => {\n        this.restoreScrollPosition()\n        // 确保滚动监听存在\n        this.addScrollListener()\n      }, 200)\n    })\n  },\n\n  // keep-alive组件失活时调用\n  deactivated() {\n    console.log('组件失活，保存状态')\n    // 先保存当前状态（使用当前已知的滚动位置，不重新获取）\n    this.saveStateWithCurrentScrollPosition()\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n\n  // 组件销毁前\n  beforeDestroy() {\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n  beforeRouteLeave(to, from, next) {\n    // 在路由离开前保存页面状态\n    // 注意：不要在这里调用saveCurrentState，因为此时表格可能已经被重置\n    // 滚动位置应该已经通过滚动监听实时保存了\n    console.log('路由离开，当前保存的滚动位置:', this.savedScrollPosition)\n    next()\n  },\n  methods: {\n    ...mapActions('todoList', ['savePageState', 'saveFilterInfo', 'savePagination', 'saveScrollPosition', 'saveTableData']),\n\n    // 保存当前状态\n    saveCurrentState() {\n      // 使用保存的滚动容器引用，或者重新查找\n      let scrollPosition = 0\n      if (this._scrollContainer) {\n        scrollPosition = this._scrollContainer.scrollTop\n      } else {\n        // 重新查找滚动容器\n        const tableWrapper = document.querySelector('.el-table__body-wrapper') ||\n                           document.querySelector('.wrap .el-table__body-wrapper')\n        if (tableWrapper) {\n          scrollPosition = tableWrapper.scrollTop\n        }\n      }\n\n      console.log('保存滚动位置:', scrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: scrollPosition,\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 使用当前已保存的滚动位置保存状态（避免重新获取可能为0的值）\n    saveStateWithCurrentScrollPosition() {\n      console.log('使用当前已保存的滚动位置:', this.savedScrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: this.savedScrollPosition, // 使用已保存的值\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 恢复页面状态的方法\n    restorePageState() {\n      // 恢复标签页\n      if (this.savedActiveName && this.savedActiveName !== this.activeName) {\n        this.activeName = this.savedActiveName\n        this.params.isHandle = this.tabRefresh[this.savedActiveName]\n      }\n\n      // 恢复筛选条件\n      if (this.savedFilterInfo && Object.keys(this.savedFilterInfo).length > 0) {\n        this.filterInfo.data = { ...this.savedFilterInfo }\n      }\n\n      // 恢复分页信息\n      if (this.savedPagination) {\n        this.tableAndPageInfo.pager.pageSize = this.savedPagination.pageSize\n        this.tableAndPageInfo.pager.pageNum = this.savedPagination.pageNum\n      }\n\n      // 如果有缓存的表格数据，先使用缓存数据，然后再获取最新数据\n      if (this.savedTableData && this.savedTableData.length > 0) {\n        this.tableAndPageInfo.tableData = this.savedTableData\n        this.tableAndPageInfo.pager.total = this.savedTotal\n      }\n\n      // 获取最新数据\n      // this.getData()\n      this.getData(this.$route.query);\n    },\n\n    // 恢复滚动位置\n    restoreScrollPosition() {\n      if (this.savedScrollPosition > 0) {\n        this.$nextTick(() => {\n          // 尝试多种可能的滚动容器选择器\n          let scrollContainer = null\n\n          // 1. 尝试标准的el-table滚动容器\n          scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n          // 2. 如果没找到，尝试comp-table的包装器\n          if (!scrollContainer) {\n            scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n          }\n\n          // 3. 如果还没找到，尝试通过comp-table组件查找\n          if (!scrollContainer) {\n            const compTableEl = this.$el.querySelector('.wrap')\n            if (compTableEl) {\n              scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n            }\n          }\n\n          console.log('恢复滚动位置:', this.savedScrollPosition, '容器:', scrollContainer)\n\n          if (scrollContainer) {\n            scrollContainer.scrollTop = this.savedScrollPosition\n\n            // 验证设置是否成功\n            setTimeout(() => {\n              console.log('滚动位置恢复验证:', scrollContainer.scrollTop)\n            }, 100)\n          } else {\n            console.warn('未找到表格滚动容器')\n          }\n        })\n      }\n    },\n\n    // 添加滚动监听\n    addScrollListener() {\n      this.$nextTick(() => {\n        // 如果已经有监听器，先移除\n        if (this._scrollHandler) {\n          this.removeScrollListener()\n        }\n\n        // 使用和恢复滚动位置相同的逻辑查找滚动容器\n        let scrollContainer = null\n\n        // 1. 尝试标准的el-table滚动容器\n        scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n        // 2. 如果没找到，尝试comp-table的包装器\n        if (!scrollContainer) {\n          scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n        }\n\n        // 3. 如果还没找到，尝试通过comp-table组件查找\n        if (!scrollContainer) {\n          const compTableEl = this.$el.querySelector('.wrap')\n          if (compTableEl) {\n            scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n          }\n        }\n\n        // 4. 最后尝试通过高度属性查找\n        if (!scrollContainer) {\n          const tables = document.querySelectorAll('.el-table__body-wrapper')\n          for (let table of tables) {\n            const tableEl = table.closest('.el-table')\n            if (tableEl && tableEl.style.height === '75vh') {\n              scrollContainer = table\n              break\n            }\n          }\n        }\n\n        console.log('添加滚动监听的容器:', scrollContainer)\n\n        if (scrollContainer) {\n          this._scrollContainer = scrollContainer // 保存引用\n          this._scrollHandler = this.throttle(() => {\n            const currentScrollTop = scrollContainer.scrollTop\n            console.log('滚动位置变化:', currentScrollTop)\n            // 直接更新store中的滚动位置\n            this.saveScrollPosition(currentScrollTop)\n          }, 300)\n          scrollContainer.addEventListener('scroll', this._scrollHandler)\n        } else {\n          console.warn('未找到表格滚动容器，无法添加滚动监听')\n        }\n      })\n    },\n\n    // 移除滚动监听\n    removeScrollListener() {\n      if (this._scrollContainer && this._scrollHandler) {\n        this._scrollContainer.removeEventListener('scroll', this._scrollHandler)\n        this._scrollHandler = null\n        this._scrollContainer = null\n        console.log('滚动监听已移除')\n      }\n    },\n\n    // 节流函数\n    throttle(func, delay) {\n      let timeoutId\n      let lastExecTime = 0\n      return function (...args) {\n        const currentTime = Date.now()\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args)\n          lastExecTime = currentTime\n        } else {\n          clearTimeout(timeoutId)\n          timeoutId = setTimeout(() => {\n            func.apply(this, args)\n            lastExecTime = Date.now()\n          }, delay - (currentTime - lastExecTime))\n        }\n      }\n    },\n\n    // 检查并修正页码\n    checkAndCorrectPageNum(records, total) {\n      const currentPage = this.tableAndPageInfo.pager.pageNum\n      const pageSize = this.tableAndPageInfo.pager.pageSize\n      const maxPage = Math.ceil(total / pageSize) || 1\n\n      console.log('页码检查:', {\n        currentPage,\n        maxPage,\n        total,\n        recordsLength: records.length,\n        hasData: records.length > 0\n      })\n\n      // 情况1：当前页超出了最大页数\n      if (currentPage > maxPage) {\n        console.log(`当前页${currentPage}超出最大页${maxPage}，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况2：当前页没有数据，但总数大于0（说明数据在其他页）\n      if (records.length === 0 && total > 0 && currentPage > 1) {\n        console.log(`当前页${currentPage}无数据但总数${total}>0，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况3：总数为0，确保在第1页\n      if (total === 0 && currentPage !== 1) {\n        console.log('总数为0，修正到第1页')\n        this.tableAndPageInfo.pager.pageNum = 1\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: 1\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      return false // 不需要修正\n    },\n\n    // 调试方法：查看页面中所有可能的滚动容器\n    debugScrollContainers() {\n      console.log('=== 调试滚动容器 ===')\n\n      // 查找所有可能的滚动容器\n      const containers = [\n        { name: 'app-container', el: document.querySelector('.app-container') },\n        { name: 'el-table__body-wrapper', el: document.querySelector('.el-table__body-wrapper') },\n        { name: 'wrap .el-table__body-wrapper', el: document.querySelector('.wrap .el-table__body-wrapper') },\n        { name: 'comp-table内的wrapper', el: this.$el.querySelector('.wrap .el-table__body-wrapper') }\n      ]\n\n      containers.forEach(container => {\n        if (container.el) {\n          console.log(`${container.name}:`, {\n            element: container.el,\n            scrollTop: container.el.scrollTop,\n            scrollHeight: container.el.scrollHeight,\n            clientHeight: container.el.clientHeight,\n            hasScroll: container.el.scrollHeight > container.el.clientHeight\n          })\n        } else {\n          console.log(`${container.name}: 未找到`)\n        }\n      })\n\n      // 查找所有el-table__body-wrapper\n      const allWrappers = document.querySelectorAll('.el-table__body-wrapper')\n      console.log('所有el-table__body-wrapper:', allWrappers)\n      allWrappers.forEach((wrapper, index) => {\n        console.log(`wrapper ${index}:`, {\n          element: wrapper,\n          scrollTop: wrapper.scrollTop,\n          scrollHeight: wrapper.scrollHeight,\n          clientHeight: wrapper.clientHeight,\n          hasScroll: wrapper.scrollHeight > wrapper.clientHeight,\n          parentTable: wrapper.closest('.el-table')\n        })\n      })\n\n      console.log('当前保存的滚动位置:', this.savedScrollPosition)\n      console.log('=== 调试结束 ===')\n    },\n    \n    async getData(param) {\n      // 合并路由参数和筛选条件\n      this.params = { ...this.params, ...param, ...this.filterInfo.data }\n\n      if (!this.params.todoUserId) {\n        this.params.todoUserId = this.$store.getters.name\n      }\n\n      let { code, data } = await listByPage({\n        ...this.params,\n        pageNum: this.tableAndPageInfo.pager.pageNum,\n        pageSize: this.tableAndPageInfo.pager.pageSize\n      })\n\n      if (code === \"0000\") {\n        // 检查页码是否需要修正\n        const needPageCorrection = this.checkAndCorrectPageNum(data.records, data.total)\n\n        if (needPageCorrection) {\n          // 页码需要修正，重新请求数据\n          console.log('页码需要修正，重新请求数据')\n          return this.getData(param)\n        }\n\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n\n        // 保存表格数据到store\n        this.saveTableData({\n          tableData: data.records,\n          total: data.total\n        })\n\n        // 数据加载完成后，延迟恢复滚动位置和添加滚动监听\n        this.$nextTick(() => {\n          // 使用setTimeout确保表格完全渲染\n          setTimeout(() => {\n            this.restoreScrollPosition()\n            this.addScrollListener()\n          }, 100)\n        })\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    // 处理筛选条件变化事件\n    handleFilterEvent(obj, data) {\n      // 实时保存筛选条件\n      this.saveFilterInfo(data)\n      // 如果是回车或者change事件，触发搜索\n      if (obj.value !== undefined) {\n        this.filterInfo.data = data\n        this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n        this.getData()\n        // 保存分页状态\n        this.savePagination({\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: 1\n        })\n      }\n    },\n\n    // 重置筛选条件\n    filterReset(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存重置后的筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    },\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      // if(row.isView==0){\n      //   await insertOrUpdateTodoItem({objId:row.objId,isView:'1'})\n      //   this.getData()\n      // }\n    },\n    deleteTodo(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          delByIds([row.objId]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.activeName = tab.name\n          this.params.isHandle = this.tabRefresh[key]\n          // 切换标签页时重置分页到第一页\n          this.tableAndPageInfo.pager.pageNum = 1\n          this.getData()\n          // 保存当前状态\n          this.saveCurrentState()\n        }\n      }\n    },\n    closeForm() {\n      this.formInfo = {};\n    },\n    goPage(row) {\n      if (row.moduleKey === \"gzplccs\" && false) {\n        //解决工作票跳转404的问题,疑似舍弃了\n        const topMenus = this.$store.getters.topMenus;\n        if (topMenus.length > 0) {\n          for (const topMenu of topMenus) {\n            if (topMenu.name === \"工作票管理\") {\n              this.$router.push({\n                path: topMenu.path,\n                query: { objId: row.businessId, module: row.moduleKey }\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        this.$router.push({\n          path: row.routePath,\n          query: { objId: row.businessId, module: row.moduleKey }\n        });\n      }\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.businessId;\n      this.processData.processDefinitionKey = row.moduleKey;\n      this.processData.businessType = row.module;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    // 监听分页变化\n    handleSizeChange(val) {\n      this.tableAndPageInfo.pager.pageSize = val\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: val,\n        pageNum: 1\n      })\n    },\n    handleCurrentChange(val) {\n      this.tableAndPageInfo.pager.pageNum = val\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: val\n      })\n    },\n    // 监听筛选条件变化\n    handleFilter(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData(data)\n      // 保存筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}