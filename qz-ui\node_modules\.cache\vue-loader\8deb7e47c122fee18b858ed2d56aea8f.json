{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue?vue&type=style&index=0&id=7dbf0cc8&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue", "mtime": 1752489465823}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5pdGVtIHsKICB3aWR0aDogOC41cmVtOwogIGhlaWdodDogMS4yNXJlbTsKfQo="}, {"version": 3, "sources": ["processTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4xBA;AACA;AACA;AACA", "file": "processTodo.vue", "sourceRoot": "src/views/activiti/dgTodoItem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane name=\"db\">\n          <span slot=\"label\"><i class=\"el-icon-s-order\"></i>待办</span>\n        </el-tab-pane>\n        <el-tab-pane name=\"yb\">\n          <span slot=\"label\"><i class=\"el-icon-s-claim\"></i>已办</span>\n        </el-tab-pane>\n      </el-tabs>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n        @handleEvent=\"handleFilterEvent\"\n        :btnHidden=\"false\"\n      ></el-filter>\n\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block;word-break : normal;\"\n          label=\"事项标题\"\n          min-width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? '被退回'\n                    : '待办理'\n                  : '已办理'\n              \"\n              class=\"item\"\n              :type=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? 'warning'\n                    : 'danger'\n                  : 'primary'\n              \"\n            >\n            </el-badge>\n            <el-button type=\"text\" size=\"small\" @click=\"goPage(scope.row)\">{{\n              scope.row.itemName\n            }}</el-button>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"100\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"跳转\"\n              class=\"el-icon-discover\"\n              @click=\"goPage(scope.row)\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.moduleKey !== 'probfbk'\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              @click=\"deleteTodo(scope.row)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <el-dialog\n      title=\"待办详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"itemName\">\n              <el-input v-model=\"formInfo.itemName\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"itemContent\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.itemContent\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"formInfo.module\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"通知时间：\" prop=\"todoTime\">\n              <el-date-picker\n                v-model=\"formInfo.todoTime\"\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport {\n  list,\n  listByPage,\n  insertOrUpdateTodoItem,\n  delByIds\n} from \"@/api/activiti/DgTodoItem\";\nimport { mapState, mapMutations, mapActions } from 'vuex'\n\nexport default {\n  components: { timeLine },\n  name: \"processTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {\n          module: \"\",\n          todoUserName: \"\",\n          handleUserName: \"\",\n          applyUserName: \"\"\n        },\n        fieldList: [\n          { label: \"待办来源\", type: \"input\", value: \"module\" },\n          { label: \"待办人\", type: \"input\", value: \"todoUserName\" },\n          { label: \"处理人\", type: \"input\", value: \"handleUserName\" },\n          { label: \"发起人\", type: \"input\", value: \"applyUserName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"module\", label: \"待办来源\", minWidth: \"120\" },\n          { prop: \"taskName\", label: \"任务名称\", minWidth: \"140\" },\n          { prop: \"isHandleCn\", label: \"是否已办\", minWidth: \"80\" },\n          { prop: \"todoUserName\", label: \"待办人名称\", minWidth: \"100\" },\n          { prop: \"handleUserName\", label: \"处理人名称\", minWidth: \"100\" },\n          { prop: \"applyUserName\", label: \"发起人名称\", minWidth: \"100\" },\n          { prop: \"todoTime\", label: \"通知时间\", minWidth: \"150\" },\n          { prop: \"handleTime\", label: \"处理时间\", minWidth: \"150\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      params: {\n        isHandle: 0\n      },\n      activeName: \"db\",\n      selectRows: [],\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {},\n      isDisabled: false,\n      timeData: [],\n      timeLineShow: false,\n      processData: {\n        processDefinitionKey: \"\",\n        businessKey: \"\",\n        businessType: \"\",\n        variables: {},\n        nextUser: \"\",\n        processType: \"complete\"\n      }\n    };\n  },\n  // watch: {\n  //   //触发计算函数时执行\n  //   qxjlObjIdChange(val) {\n  //     this.getData({ objId: val });\n  //   }\n  // },\n  //计算函数\n  computed: {\n    ...mapState('todoList', {\n      savedActiveName: 'activeName',\n      savedFilterInfo: 'filterInfo',\n      savedPagination: 'pagination',\n      savedScrollPosition: 'scrollPosition',\n      savedTableData: 'tableData',\n      savedTotal: 'total'\n    })\n  },\n  created() {\n    // 从 store 恢复状态\n    this.restorePageState()\n  },\n  mounted() {\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      this.restoreScrollPosition()\n      // 添加滚动监听\n      this.addScrollListener()\n    })\n  },\n\n  // keep-alive组件激活时调用\n  activated() {\n    // 每次激活时恢复滚动位置\n    this.$nextTick(() => {\n      // 使用setTimeout确保页面完全渲染\n      setTimeout(() => {\n        this.restoreScrollPosition()\n        // 确保滚动监听存在\n        this.addScrollListener()\n      }, 200)\n    })\n  },\n\n  // keep-alive组件失活时调用\n  deactivated() {\n    console.log('组件失活，保存状态')\n    // 先保存当前状态（使用当前已知的滚动位置，不重新获取）\n    this.saveStateWithCurrentScrollPosition()\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n\n  // 组件销毁前\n  beforeDestroy() {\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n  beforeRouteLeave(to, from, next) {\n    // 在路由离开前保存页面状态\n    // 注意：不要在这里调用saveCurrentState，因为此时表格可能已经被重置\n    // 滚动位置应该已经通过滚动监听实时保存了\n    console.log('路由离开，当前保存的滚动位置:', this.savedScrollPosition)\n    next()\n  },\n  methods: {\n    ...mapActions('todoList', ['savePageState', 'saveFilterInfo', 'savePagination', 'saveScrollPosition', 'saveTableData']),\n\n    // 保存当前状态\n    saveCurrentState() {\n      // 使用保存的滚动容器引用，或者重新查找\n      let scrollPosition = 0\n      if (this._scrollContainer) {\n        scrollPosition = this._scrollContainer.scrollTop\n      } else {\n        // 重新查找滚动容器\n        const tableWrapper = document.querySelector('.el-table__body-wrapper') ||\n                           document.querySelector('.wrap .el-table__body-wrapper')\n        if (tableWrapper) {\n          scrollPosition = tableWrapper.scrollTop\n        }\n      }\n\n      console.log('保存滚动位置:', scrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: scrollPosition,\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 使用当前已保存的滚动位置保存状态（避免重新获取可能为0的值）\n    saveStateWithCurrentScrollPosition() {\n      console.log('使用当前已保存的滚动位置:', this.savedScrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: this.savedScrollPosition, // 使用已保存的值\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 恢复页面状态的方法\n    restorePageState() {\n      // 恢复标签页\n      if (this.savedActiveName && this.savedActiveName !== this.activeName) {\n        this.activeName = this.savedActiveName\n        this.params.isHandle = this.tabRefresh[this.savedActiveName]\n      }\n\n      // 恢复筛选条件\n      if (this.savedFilterInfo && Object.keys(this.savedFilterInfo).length > 0) {\n        this.filterInfo.data = { ...this.savedFilterInfo }\n      }\n\n      // 恢复分页信息\n      if (this.savedPagination) {\n        this.tableAndPageInfo.pager.pageSize = this.savedPagination.pageSize\n        this.tableAndPageInfo.pager.pageNum = this.savedPagination.pageNum\n      }\n\n      // 如果有缓存的表格数据，先使用缓存数据，然后再获取最新数据\n      if (this.savedTableData && this.savedTableData.length > 0) {\n        this.tableAndPageInfo.tableData = this.savedTableData\n        this.tableAndPageInfo.pager.total = this.savedTotal\n      }\n\n      // 获取最新数据\n      // this.getData()\n      this.getData(this.$route.query);\n    },\n\n    // 恢复滚动位置\n    restoreScrollPosition() {\n      if (this.savedScrollPosition > 0) {\n        this.$nextTick(() => {\n          // 尝试多种可能的滚动容器选择器\n          let scrollContainer = null\n\n          // 1. 尝试标准的el-table滚动容器\n          scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n          // 2. 如果没找到，尝试comp-table的包装器\n          if (!scrollContainer) {\n            scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n          }\n\n          // 3. 如果还没找到，尝试通过comp-table组件查找\n          if (!scrollContainer) {\n            const compTableEl = this.$el.querySelector('.wrap')\n            if (compTableEl) {\n              scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n            }\n          }\n\n          console.log('恢复滚动位置:', this.savedScrollPosition, '容器:', scrollContainer)\n\n          if (scrollContainer) {\n            scrollContainer.scrollTop = this.savedScrollPosition\n\n            // 验证设置是否成功\n            setTimeout(() => {\n              console.log('滚动位置恢复验证:', scrollContainer.scrollTop)\n            }, 100)\n          } else {\n            console.warn('未找到表格滚动容器')\n          }\n        })\n      }\n    },\n\n    // 添加滚动监听\n    addScrollListener() {\n      this.$nextTick(() => {\n        // 如果已经有监听器，先移除\n        if (this._scrollHandler) {\n          this.removeScrollListener()\n        }\n\n        // 使用和恢复滚动位置相同的逻辑查找滚动容器\n        let scrollContainer = null\n\n        // 1. 尝试标准的el-table滚动容器\n        scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n        // 2. 如果没找到，尝试comp-table的包装器\n        if (!scrollContainer) {\n          scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n        }\n\n        // 3. 如果还没找到，尝试通过comp-table组件查找\n        if (!scrollContainer) {\n          const compTableEl = this.$el.querySelector('.wrap')\n          if (compTableEl) {\n            scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n          }\n        }\n\n        // 4. 最后尝试通过高度属性查找\n        if (!scrollContainer) {\n          const tables = document.querySelectorAll('.el-table__body-wrapper')\n          for (let table of tables) {\n            const tableEl = table.closest('.el-table')\n            if (tableEl && tableEl.style.height === '75vh') {\n              scrollContainer = table\n              break\n            }\n          }\n        }\n\n        console.log('添加滚动监听的容器:', scrollContainer)\n\n        if (scrollContainer) {\n          this._scrollContainer = scrollContainer // 保存引用\n          this._scrollHandler = this.throttle(() => {\n            const currentScrollTop = scrollContainer.scrollTop\n            console.log('滚动位置变化:', currentScrollTop)\n            // 直接更新store中的滚动位置\n            this.saveScrollPosition(currentScrollTop)\n          }, 300)\n          scrollContainer.addEventListener('scroll', this._scrollHandler)\n        } else {\n          console.warn('未找到表格滚动容器，无法添加滚动监听')\n        }\n      })\n    },\n\n    // 移除滚动监听\n    removeScrollListener() {\n      if (this._scrollContainer && this._scrollHandler) {\n        this._scrollContainer.removeEventListener('scroll', this._scrollHandler)\n        this._scrollHandler = null\n        this._scrollContainer = null\n        console.log('滚动监听已移除')\n      }\n    },\n\n    // 节流函数\n    throttle(func, delay) {\n      let timeoutId\n      let lastExecTime = 0\n      return function (...args) {\n        const currentTime = Date.now()\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args)\n          lastExecTime = currentTime\n        } else {\n          clearTimeout(timeoutId)\n          timeoutId = setTimeout(() => {\n            func.apply(this, args)\n            lastExecTime = Date.now()\n          }, delay - (currentTime - lastExecTime))\n        }\n      }\n    },\n\n    // 检查并修正页码\n    checkAndCorrectPageNum(records, total) {\n      const currentPage = this.tableAndPageInfo.pager.pageNum\n      const pageSize = this.tableAndPageInfo.pager.pageSize\n      const maxPage = Math.ceil(total / pageSize) || 1\n\n      console.log('页码检查:', {\n        currentPage,\n        maxPage,\n        total,\n        recordsLength: records.length,\n        hasData: records.length > 0\n      })\n\n      // 情况1：当前页超出了最大页数\n      if (currentPage > maxPage) {\n        console.log(`当前页${currentPage}超出最大页${maxPage}，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况2：当前页没有数据，但总数大于0（说明数据在其他页）\n      if (records.length === 0 && total > 0 && currentPage > 1) {\n        console.log(`当前页${currentPage}无数据但总数${total}>0，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况3：总数为0，确保在第1页\n      if (total === 0 && currentPage !== 1) {\n        console.log('总数为0，修正到第1页')\n        this.tableAndPageInfo.pager.pageNum = 1\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: 1\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      return false // 不需要修正\n    },\n\n    // 调试方法：查看页面中所有可能的滚动容器\n    debugScrollContainers() {\n      console.log('=== 调试滚动容器 ===')\n\n      // 查找所有可能的滚动容器\n      const containers = [\n        { name: 'app-container', el: document.querySelector('.app-container') },\n        { name: 'el-table__body-wrapper', el: document.querySelector('.el-table__body-wrapper') },\n        { name: 'wrap .el-table__body-wrapper', el: document.querySelector('.wrap .el-table__body-wrapper') },\n        { name: 'comp-table内的wrapper', el: this.$el.querySelector('.wrap .el-table__body-wrapper') }\n      ]\n\n      containers.forEach(container => {\n        if (container.el) {\n          console.log(`${container.name}:`, {\n            element: container.el,\n            scrollTop: container.el.scrollTop,\n            scrollHeight: container.el.scrollHeight,\n            clientHeight: container.el.clientHeight,\n            hasScroll: container.el.scrollHeight > container.el.clientHeight\n          })\n        } else {\n          console.log(`${container.name}: 未找到`)\n        }\n      })\n\n      // 查找所有el-table__body-wrapper\n      const allWrappers = document.querySelectorAll('.el-table__body-wrapper')\n      console.log('所有el-table__body-wrapper:', allWrappers)\n      allWrappers.forEach((wrapper, index) => {\n        console.log(`wrapper ${index}:`, {\n          element: wrapper,\n          scrollTop: wrapper.scrollTop,\n          scrollHeight: wrapper.scrollHeight,\n          clientHeight: wrapper.clientHeight,\n          hasScroll: wrapper.scrollHeight > wrapper.clientHeight,\n          parentTable: wrapper.closest('.el-table')\n        })\n      })\n\n      console.log('当前保存的滚动位置:', this.savedScrollPosition)\n      console.log('=== 调试结束 ===')\n    },\n    \n    async getData(param) {\n      // 合并路由参数和筛选条件\n      this.params = { ...this.params, ...param, ...this.filterInfo.data }\n\n      if (!this.params.todoUserId) {\n        this.params.todoUserId = this.$store.getters.name\n      }\n\n      let { code, data } = await listByPage({\n        ...this.params,\n        pageNum: this.tableAndPageInfo.pager.pageNum,\n        pageSize: this.tableAndPageInfo.pager.pageSize\n      })\n\n      if (code === \"0000\") {\n        // 检查页码是否需要修正\n        const needPageCorrection = this.checkAndCorrectPageNum(data.records, data.total)\n\n        if (needPageCorrection) {\n          // 页码需要修正，重新请求数据\n          console.log('页码需要修正，重新请求数据')\n          return this.getData(param)\n        }\n\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n\n        // 保存表格数据到store\n        this.saveTableData({\n          tableData: data.records,\n          total: data.total\n        })\n\n        // 数据加载完成后，延迟恢复滚动位置和添加滚动监听\n        this.$nextTick(() => {\n          // 使用setTimeout确保表格完全渲染\n          setTimeout(() => {\n            this.restoreScrollPosition()\n            this.addScrollListener()\n          }, 100)\n        })\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    // 处理筛选条件变化事件\n    handleFilterEvent(obj, data) {\n      // 实时保存筛选条件\n      this.saveFilterInfo(data)\n      // 如果是回车或者change事件，触发搜索\n      if (obj.value !== undefined) {\n        this.filterInfo.data = data\n        this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n        this.getData()\n        // 保存分页状态\n        this.savePagination({\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: 1\n        })\n      }\n    },\n\n    // 重置筛选条件\n    filterReset(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存重置后的筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    },\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      // if(row.isView==0){\n      //   await insertOrUpdateTodoItem({objId:row.objId,isView:'1'})\n      //   this.getData()\n      // }\n    },\n    deleteTodo(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          delByIds([row.objId]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.activeName = tab.name\n          this.params.isHandle = this.tabRefresh[key]\n          // 切换标签页时重置分页到第一页\n          this.tableAndPageInfo.pager.pageNum = 1\n          this.getData()\n          // 保存当前状态\n          this.saveCurrentState()\n        }\n      }\n    },\n    closeForm() {\n      this.formInfo = {};\n    },\n    goPage(row) {\n      if (row.moduleKey === \"gzplccs\" && false) {\n        //解决工作票跳转404的问题,疑似舍弃了\n        const topMenus = this.$store.getters.topMenus;\n        if (topMenus.length > 0) {\n          for (const topMenu of topMenus) {\n            if (topMenu.name === \"工作票管理\") {\n              this.$router.push({\n                path: topMenu.path,\n                query: { objId: row.businessId, module: row.moduleKey }\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        this.$router.push({\n          path: row.routePath,\n          query: { objId: row.businessId, module: row.moduleKey }\n        });\n      }\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.businessId;\n      this.processData.processDefinitionKey = row.moduleKey;\n      this.processData.businessType = row.module;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    // 监听分页变化\n    handleSizeChange(val) {\n      this.tableAndPageInfo.pager.pageSize = val\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: val,\n        pageNum: 1\n      })\n    },\n    handleCurrentChange(val) {\n      this.tableAndPageInfo.pager.pageNum = val\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: val\n      })\n    },\n    // 监听筛选条件变化\n    handleFilter(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData(data)\n      // 保存筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}